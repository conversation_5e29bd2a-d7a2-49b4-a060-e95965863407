const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const scoreElement = document.getElementById('score');
const highScoreElement = document.getElementById('highScore');
const speedElement = document.getElementById('speed');
const lengthElement = document.getElementById('length');
const restartBtn = document.getElementById('restartBtn');
const speedSlider = document.getElementById('speedSlider');
const speedValue = document.getElementById('speedValue');

const gridSize = 20;
const tileCount = canvas.width / gridSize;

let snake = [{x: 10, y: 10}];
let food = {};
let dx = 0;
let dy = 0;
let score = 0;
let highScore = localStorage.getItem('snakeHighScore') || 0;
let gameSpeed = 5;
let gameInterval;

// Initialize display
highScoreElement.textContent = highScore;
updateGameStats();

function randomFood() {
    do {
        food = {
            x: Math.floor(Math.random() * tileCount),
            y: Math.floor(Math.random() * tileCount)
        };
    } while (snake.some(segment => segment.x === food.x && segment.y === food.y));
}

function drawGame() {
    // Clear canvas
    ctx.fillStyle = '#2c3e50';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw snake
    ctx.fillStyle = '#27ae60';
    snake.forEach((segment, index) => {
        if (index === 0) {
            // Snake head - slightly different color
            ctx.fillStyle = '#2ecc71';
        } else {
            ctx.fillStyle = '#27ae60';
        }
        ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize - 2, gridSize - 2);
    });
    
    // Draw food
    ctx.fillStyle = '#e74c3c';
    ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize - 2, gridSize - 2);
}

function moveSnake() {
    if (dx === 0 && dy === 0) return; // Don't move if no direction set
    
    const head = {x: snake[0].x + dx, y: snake[0].y + dy};
    
    // Check wall collision
    if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
        gameOver();
        return;
    }
    
    // Check self collision
    for (let segment of snake) {
        if (head.x === segment.x && head.y === segment.y) {
            gameOver();
            return;
        }
    }
    
    snake.unshift(head);
    
    // Check food collision
    if (head.x === food.x && head.y === food.y) {
        score += 10 * gameSpeed; // Higher speed = more points
        updateGameStats();
        randomFood();
        
        // Increase speed slightly as snake grows
        if (snake.length % 5 === 0 && gameSpeed < 10) {
            gameSpeed = Math.min(10, gameSpeed + 0.5);
            speedElement.textContent = gameSpeed.toFixed(1);
            restartGameLoop();
        }
    } else {
        snake.pop();
    }
}

function updateGameStats() {
    scoreElement.textContent = score;
    lengthElement.textContent = snake.length;
    speedElement.textContent = gameSpeed.toFixed(1);
    
    if (score > highScore) {
        highScore = score;
        highScoreElement.textContent = highScore;
        localStorage.setItem('snakeHighScore', highScore);
    }
}

function gameOver() {
    clearInterval(gameInterval);
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    ctx.fillStyle = 'white';
    ctx.font = '30px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Game Over!', canvas.width / 2, canvas.height / 2 - 20);
    
    ctx.font = '16px Arial';
    ctx.fillText(`Final Score: ${score}`, canvas.width / 2, canvas.height / 2 + 20);
    ctx.fillText('Click Restart to play again', canvas.width / 2, canvas.height / 2 + 45);
}

function resetGame() {
    clearInterval(gameInterval);
    snake = [{x: 10, y: 10}];
    dx = 0;
    dy = 0;
    score = 0;
    gameSpeed = parseInt(speedSlider.value);
    updateGameStats();
    randomFood();
    startGameLoop();
}

function startGameLoop() {
    const speed = 200 - (gameSpeed - 1) * 18; // Convert speed to interval
    gameInterval = setInterval(gameLoop, speed);
}

function restartGameLoop() {
    clearInterval(gameInterval);
    startGameLoop();
}

function gameLoop() {
    moveSnake();
    drawGame();
}

// Speed slider control
speedSlider.addEventListener('input', (e) => {
    if (dx === 0 && dy === 0) { // Only allow speed change when game hasn't started
        gameSpeed = parseInt(e.target.value);
        speedValue.textContent = gameSpeed;
        speedElement.textContent = gameSpeed;
    } else {
        speedValue.textContent = gameSpeed;
    }
});

// Keyboard controls
document.addEventListener('keydown', (e) => {
    if (e.key === 'ArrowUp' && dy !== 1) {
        dx = 0;
        dy = -1;
    } else if (e.key === 'ArrowDown' && dy !== -1) {
        dx = 0;
        dy = 1;
    } else if (e.key === 'ArrowLeft' && dx !== 1) {
        dx = -1;
        dy = 0;
    } else if (e.key === 'ArrowRight' && dx !== -1) {
        dx = 1;
        dy = 0;
    }
});

restartBtn.addEventListener('click', resetGame);

// Initialize game
randomFood();
startGameLoop();
