const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const scoreElement = document.getElementById('score');
const highScoreElement = document.getElementById('highScore');
const speedElement = document.getElementById('speed');
const lengthElement = document.getElementById('length');
const restartBtn = document.getElementById('restartBtn');
const speedSlider = document.getElementById('speedSlider');
const speedValue = document.getElementById('speedValue');

const gridSize = 20;
const tileCount = canvas.width / gridSize;

let snake = [{x: 10, y: 10}];
let food = {};
let dx = 0;
let dy = 0;
let score = 0;
let highScore = localStorage.getItem('snakeHighScore') || 0;
let gameSpeed = 5;
let gameInterval;
let gameStarted = false;
let gamePaused = false;

// Initialize display
highScoreElement.textContent = highScore;
updateGameStats();

function randomFood() {
    do {
        food = {
            x: Math.floor(Math.random() * tileCount),
            y: Math.floor(Math.random() * tileCount)
        };
    } while (snake.some(segment => segment.x === food.x && segment.y === food.y));
}

function drawGame() {
    // Clear canvas
    ctx.fillStyle = '#2c3e50';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw grid lines for better visual
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    for (let i = 0; i <= tileCount; i++) {
        ctx.beginPath();
        ctx.moveTo(i * gridSize, 0);
        ctx.lineTo(i * gridSize, canvas.height);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(0, i * gridSize);
        ctx.lineTo(canvas.width, i * gridSize);
        ctx.stroke();
    }

    // Draw snake with gradient effect
    snake.forEach((segment, index) => {
        if (index === 0) {
            // Snake head - with glow effect
            ctx.fillStyle = '#2ecc71';
            ctx.shadowColor = '#2ecc71';
            ctx.shadowBlur = 10;
        } else {
            // Snake body - gradient from head to tail
            const alpha = 1 - (index / snake.length) * 0.3;
            ctx.fillStyle = `rgba(39, 174, 96, ${alpha})`;
            ctx.shadowBlur = 0;
        }

        // Rounded rectangles for snake segments
        const x = segment.x * gridSize + 1;
        const y = segment.y * gridSize + 1;
        const size = gridSize - 2;

        ctx.beginPath();
        ctx.roundRect(x, y, size, size, 4);
        ctx.fill();
    });

    // Reset shadow
    ctx.shadowBlur = 0;

    // Draw food with pulsing effect
    const time = Date.now() * 0.005;
    const pulse = Math.sin(time) * 0.1 + 0.9;
    const foodSize = (gridSize - 2) * pulse;
    const offset = (gridSize - foodSize) / 2;

    ctx.fillStyle = '#e74c3c';
    ctx.shadowColor = '#e74c3c';
    ctx.shadowBlur = 15;

    ctx.beginPath();
    ctx.roundRect(
        food.x * gridSize + offset,
        food.y * gridSize + offset,
        foodSize,
        foodSize,
        6
    );
    ctx.fill();

    // Reset shadow
    ctx.shadowBlur = 0;

    // Show start message if game hasn't started
    if (!gameStarted) {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = 'white';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Press Arrow Key to Start', canvas.width / 2, canvas.height / 2 - 10);

        ctx.font = '16px Arial';
        ctx.fillText('Use WASD or Arrow Keys to control', canvas.width / 2, canvas.height / 2 + 20);
        ctx.fillText('Press SPACE to pause/resume', canvas.width / 2, canvas.height / 2 + 45);
    }

    // Show pause message
    if (gamePaused && gameStarted) {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = 'white';
        ctx.font = 'bold 30px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('PAUSED', canvas.width / 2, canvas.height / 2);

        ctx.font = '16px Arial';
        ctx.fillText('Press SPACE to resume', canvas.width / 2, canvas.height / 2 + 30);
    }
}

function moveSnake() {
    if (dx === 0 && dy === 0) return; // Don't move if no direction set
    if (!gameStarted || gamePaused) return; // Don't move if game not started or paused

    const head = {x: snake[0].x + dx, y: snake[0].y + dy};

    // Check wall collision
    if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
        gameOver();
        return;
    }

    // Check self collision
    for (let segment of snake) {
        if (head.x === segment.x && head.y === segment.y) {
            gameOver();
            return;
        }
    }

    snake.unshift(head);

    // Check food collision
    if (head.x === food.x && head.y === food.y) {
        score += 10 * Math.floor(gameSpeed); // Higher speed = more points
        updateGameStats();
        randomFood();

        // Increase speed slightly as snake grows (more gradual)
        if (snake.length % 3 === 0 && gameSpeed < 10) {
            gameSpeed = Math.min(10, gameSpeed + 0.2);
            speedElement.textContent = gameSpeed.toFixed(1);
            restartGameLoop();
        }
    } else {
        snake.pop();
    }
}

function updateGameStats() {
    scoreElement.textContent = score;
    lengthElement.textContent = snake.length;
    speedElement.textContent = gameSpeed.toFixed(1);
    
    if (score > highScore) {
        highScore = score;
        highScoreElement.textContent = highScore;
        localStorage.setItem('snakeHighScore', highScore);
    }
}

function gameOver() {
    clearInterval(gameInterval);
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    ctx.fillStyle = 'white';
    ctx.font = '30px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Game Over!', canvas.width / 2, canvas.height / 2 - 20);
    
    ctx.font = '16px Arial';
    ctx.fillText(`Final Score: ${score}`, canvas.width / 2, canvas.height / 2 + 20);
    ctx.fillText('Click Restart to play again', canvas.width / 2, canvas.height / 2 + 45);
}

function resetGame() {
    clearInterval(gameInterval);
    snake = [{x: 10, y: 10}];
    dx = 0;
    dy = 0;
    score = 0;
    gameSpeed = parseFloat(speedSlider.value);
    gameStarted = false;
    gamePaused = false;
    updateGameStats();
    randomFood();
    startGameLoop();
}

function startGameLoop() {
    clearInterval(gameInterval);
    // Smoother speed calculation
    const baseSpeed = 300;
    const speedMultiplier = Math.pow(0.85, gameSpeed - 1);
    const interval = Math.max(50, baseSpeed * speedMultiplier);
    gameInterval = setInterval(gameLoop, interval);
}

function restartGameLoop() {
    startGameLoop();
}

function gameLoop() {
    moveSnake();
    drawGame();
}

function pauseGame() {
    gamePaused = !gamePaused;
    if (!gamePaused && gameStarted) {
        // Resume animation loop
        drawGame();
    }
}

// Speed slider control
speedSlider.addEventListener('input', (e) => {
    if (!gameStarted) { // Only allow speed change when game hasn't started
        gameSpeed = parseFloat(e.target.value);
        speedValue.textContent = gameSpeed;
        speedElement.textContent = gameSpeed.toFixed(1);
    } else {
        // Reset slider to current game speed if game is running
        e.target.value = gameSpeed;
        speedValue.textContent = gameSpeed.toFixed(1);
    }
});

// Keyboard controls
document.addEventListener('keydown', (e) => {
    // Prevent default behavior for game keys
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'KeyW', 'KeyA', 'KeyS', 'KeyD', 'Space'].includes(e.code)) {
        e.preventDefault();
    }

    // Pause/Resume with spacebar
    if (e.code === 'Space') {
        if (gameStarted) {
            pauseGame();
        }
        return;
    }

    // Don't change direction if game is paused
    if (gamePaused) return;

    // Movement controls (Arrow keys and WASD)
    const newDirection = getNewDirection(e.code);
    if (newDirection) {
        const { newDx, newDy } = newDirection;

        // Start game on first movement
        if (!gameStarted) {
            gameStarted = true;
        }

        // Prevent reverse direction
        if ((newDx === -dx && newDy === -dy) && (dx !== 0 || dy !== 0)) {
            return;
        }

        dx = newDx;
        dy = newDy;
    }
});

function getNewDirection(keyCode) {
    switch (keyCode) {
        case 'ArrowUp':
        case 'KeyW':
            return { newDx: 0, newDy: -1 };
        case 'ArrowDown':
        case 'KeyS':
            return { newDx: 0, newDy: 1 };
        case 'ArrowLeft':
        case 'KeyA':
            return { newDx: -1, newDy: 0 };
        case 'ArrowRight':
        case 'KeyD':
            return { newDx: 1, newDy: 0 };
        default:
            return null;
    }
}

restartBtn.addEventListener('click', resetGame);

// Initialize game
randomFood();
drawGame(); // Draw initial state
startGameLoop();
