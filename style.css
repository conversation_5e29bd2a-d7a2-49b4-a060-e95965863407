body {
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
}

.game-container {
    text-align: center;
    background: rgba(255, 255, 255, 0.15);
    padding: 25px;
    border-radius: 20px;
    backdrop-filter: blur(15px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 500px;
    animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

h1 {
    color: white;
    margin-bottom: 20px;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
    font-size: 2.5em;
    font-weight: bold;
    letter-spacing: 2px;
}

.game-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 20px;
}

.score, .high-score, .speed, .length {
    color: white;
    font-size: 16px;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.15);
    padding: 12px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.score:hover, .high-score:hover, .speed:hover, .length:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

#gameCanvas {
    border: 4px solid rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    background-color: #2c3e50;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

#gameCanvas:hover {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.6);
    transform: translateY(-2px);
}

.controls {
    margin-top: 20px;
}

.speed-controls {
    margin-bottom: 20px;
    color: white;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.speed-controls label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    font-size: 16px;
}

#speedSlider {
    width: 200px;
    margin: 0 10px;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.3);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

#speedSlider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #27ae60;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

#speedSlider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #27ae60;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

#speedValue {
    font-weight: bold;
    color: #27ae60;
    font-size: 18px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.control-instructions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 20px 0;
}

.instruction-group {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.instruction-group h3 {
    color: white;
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.instruction-group p {
    color: rgba(255, 255, 255, 0.9);
    margin: 5px 0;
    font-size: 14px;
    line-height: 1.4;
}

#restartBtn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

#restartBtn:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.6);
}

#restartBtn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(231, 76, 60, 0.4);
}

@media (max-width: 600px) {
    .game-container {
        margin: 10px;
        padding: 20px;
    }

    .control-instructions {
        grid-template-columns: 1fr;
    }

    #gameCanvas {
        width: 100%;
        max-width: 400px;
        height: auto;
    }
}
