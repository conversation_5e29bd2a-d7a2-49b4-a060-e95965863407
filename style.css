body {
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: Arial, sans-serif;
}

.game-container {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

h1 {
    color: white;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.game-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.score, .high-score, .speed, .length {
    color: white;
    font-size: 16px;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px;
    border-radius: 8px;
}

#gameCanvas {
    border: 3px solid white;
    border-radius: 10px;
    background-color: #2c3e50;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.controls {
    margin-top: 15px;
}

.speed-controls {
    margin-bottom: 15px;
    color: white;
}

.speed-controls label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

#speedSlider {
    width: 200px;
    margin: 0 10px;
}

#speedValue {
    font-weight: bold;
    color: #27ae60;
}

.controls p {
    color: white;
    margin: 10px 0;
}

#restartBtn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s;
}

#restartBtn:hover {
    background: #c0392b;
}
